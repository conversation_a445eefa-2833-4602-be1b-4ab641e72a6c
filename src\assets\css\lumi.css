/* Animations */

body {
    background: #000;
}

div,
p {
    font-family: "Roboto", Helvetica, Arial, sans-serif;
}

strong {
    font-weight: 500 !important;
}

.main-page-content {
    animation: slide-in 0.1s ease-in-out forwards;
    -webkit-animation: slide-in 0.1s ease-in-out forwards;
}

.v-enter-active {
    animation: slide-in 0.3s ease-in-out forwards;
    -webkit-animation: slide-in 0.3s ease-in-out forwards;
}

@keyframes slide-in {
    0% {
        transform: translateY(3px);
        opacity: 0
    }

    100% {
        transform: translateY(0%);
        opacity: 1
    }
}

@-webkit-keyframes slide-in {
    0% {
        transform: translateY(3px);
        opacity: 0
    }

    100% {
        transform: translateY(0%);
        opacity: 1
    }
}

.progress-bar {
    animation: fill-in 1.5s ease-in-out forwards;
    animation-fill-mode: both;
}

@keyframes fill-in {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

@-webkit-keyframes fill-in {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

/* -------------------- */

:root {
    --primary-background: #fbfdfe;
}

html {
    overflow: hidden;
}

/* Fix para scroll das páginas com LumiSidenav - Sobrescrever classes conflitantes */
.main-content.position-relative.max-height-vh-100.h-100.overflow-x-hidden {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: calc(100vh - 48px) !important; /* Altura total menos navbar */
    max-height: calc(100vh - 48px) !important;
    width: 100% !important;
    margin-top: 48px !important; /* Espaço para a navbar */
}

/* Quando há sidenav, ajustar a largura do conteúdo para não ficar atrás */
.user-access:has(.lumi-sidenav) .main-content.position-relative.max-height-vh-100.h-100.overflow-x-hidden {
    width: calc(100% - 16rem) !important; /* Largura total menos largura da sidenav */
}

/* Fallback para navegadores que não suportam :has() */
@supports not selector(:has(*)) {
    .user-access .main-content.position-relative.max-height-vh-100.h-100.overflow-x-hidden {
        width: calc(100% - 16rem) !important;
    }
}

/* Ajustes responsivos */
@media (max-width: 991.98px) {
    .user-access:has(.lumi-sidenav) .main-content.position-relative.max-height-vh-100.h-100.overflow-x-hidden,
    .user-access .main-content.position-relative.max-height-vh-100.h-100.overflow-x-hidden {
        width: 100% !important;
    }
}

.center-self {
    margin: 0 auto;
}

.text-center {
    text-align: center !important;
}

body {
    background-color: var(--primary-background) !important;
}

.main-content {
    max-width: 100vw;
    margin: 0 auto;
}

.bg-lumi {
    background-color: var(--primary-background) !important;
}

.sidenav {
    border: none !important;
    border-left-width: 1px !important;
    border-left-style: solid !important;
    /* border-left-color: linear-gradient(0deg, #FFF, #FFF, #fbfdfe) !important; */
    border-image: linear-gradient(180deg, #cbdfe9, #b4cad8, #fbfdfe) 30 !important;
    /* border-radius: 0px !important; */

    /* Garantir que a sidenav ocupe toda a altura disponível (menos navbar) */
    height: calc(100vh - 48px) !important;
    min-height: calc(100vh - 48px) !important;
}

/* Garantir que a LumiSidenav específica ocupe toda a altura disponível */
#sidenav-main {
    height: calc(100vh - 48px) !important;
    min-height: calc(100vh - 48px) !important;
    top: 48px !important;
    bottom: 0 !important;
    /* Garantir que a sidenav fique sempre visível */
    z-index: 1000 !important;
    /* Garantir largura fixa */
    width: 16rem !important;
    max-width: 16rem !important;
}

.sidenav-header {
    background: #FAFAFA;
}

.nav-link {
    background: #edf3f8;
    margin-bottom: 7px !important;
    border: 1px solid #CCC;
    height: 45px;
    min-width: 90px;
    font-weight: 400 !important;
}

.nav-link.nav-tab {
    height: 3.8rem;
}

.nav-link:hover {
    color: rgb(28, 137, 226) !important;
    border-color: rgb(28, 137, 226);
}

.v-tab.v-tab.v-btn {
    min-width: 70px;
}

.v-tab {
    border-right-width: 2px;
    border-right-color: rgba(0, 0, 0, 0.06);
}

.v-tab:first-child {
    border-left-width: 2px;
    border-left-color: rgba(0, 0, 0, 0.06);
    border-right: none;
}

.v-tab-item--selected {
    /* background: rgba(0,0,0,0.15) !important; */
    background: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.02);
}


.bg-gradient-lumi {
    background-image: linear-gradient(225deg, #ececec 0%, #e3dddd 100%)
}

.login-page-logo {
    margin: 0 auto;
    width: 210px;
}

.selector-tab-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.selector-tab-item {
    background: #F8F8F8;
    font-size: 13pt;
    width: 50%;
    text-align: center;
    padding-top: 5px;
    border: 1px solid #DDD;
}

div>.selector-tab-item:first-child {
    border-radius: 3px 0px 0px 3px;
}

div>.selector-tab-item:last-child {
    border-radius: 0px 3px 3px 0px;
}

.selector-tab-item.active {
    background: #FFF !important;
    color: rgb(28, 137, 226);
    border: 1px solid rgb(28, 137, 226);
    border-right: 1px solid rgb(28, 137, 226) !important;
}

.selector-tab-item:hover {
    cursor: pointer;
    background: linear-gradient(to right, #FFF, #F8F8F8, #FFF)
}

.selector-tab-item:first-child {
    border-right: 1px solid #EEE;
}

.selector-tab-item::after {
    content: '';
    background-image: linear-gradient(to right, #FFF, #EEE, rgb(187, 187, 232), #EEE, #FFF);
    height: 1px;
    /*for horizontal border; width for vertical*/
    display: block;
    margin-top: 4px;
}

.selector-tab-divider {
    height: 100%;
    width: 1px;
}

.clickable {
    cursor: pointer;
}

.table-header-item {
    color: rgb(var(--v-theme-secondary)) !important;
}

.table-body-item {
    /* padding: 3px !important; */
}

.info-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    border-radius: 5px;
    padding: 5px 10px;
    margin-top: 5px;
    border-width: 2px;
}

.info-container.good, .info-container.positivo {
    border-color: rgb(102, 204, 102);
    color: rgb(46, 179, 46);
}

.info-container.bad, .info-container.negativo {
    border-color: rgb(202, 114, 114);
    color: rgb(179, 46, 46);
}

.info-container.neutral, .info-container.neutro {
    border-color: rgb(160, 160, 160);
    color: rgb(160, 160, 160);
}

.info-container.attention, .info-container.atencao {
    border-color: rgb(226, 178, 88);
    color: rgb(216, 149, 25);
}

.info-container div:last-child {
    width: 100%;
    text-align: center;
}

.form-select {
    padding: 7px 10px;
}

.form-select:active,
.form-select:focus {
    border: 1px solid #6ca5db;
}

.page-width-container {
    background: linear-gradient(to right, #FFF, #EEE, #FFF);
}

.page-width {
    -webkit-box-shadow: 1px 1px 25px 0px rgba(138, 138, 138, 0.2);
    -moz-box-shadow: 1px 1px 25px 0px rgba(138, 138, 138, 0.2);
    box-shadow: 1px 1px 25px 0px rgba(138, 138, 138, 0.2);
    background: #FFF;
    max-width: 1300px;
    margin: 0 auto;
    border-left: 2px solid rgba(222, 222, 222, 0.6);
    border-right: 2px solid rgba(222, 222, 222, 0.6);
}

input.form-control,
textarea.form-control {
    border: 1px solid #DDD;
    padding: 8px 12px;
    font-size: 12pt;
}

input.form-control:focus,
textarea.form-control:focus {
    border-color: #6ca5db !important;
}

input.form-control:active,
textarea.form-control:active {
    border-color: #6ca5db !important;
}

.nav-btn-container {
    width: 100%;
    text-align: center;
    padding: 0px 0px;
}

.nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding-left: 0.85rem; /* Reduzido de 1rem */
    padding-right: 0.85rem; /* Reduzido de 1rem */
    border-radius: 0.5rem;
    margin: 0 0.85rem; /* Reduzido de 1rem */
    color: #344767 !important;
    background: #ffffff;
    margin-bottom: 8px !important;
    border: 1px solid rgba(0, 0, 0, 0.08);
    height: 46px; /* Reduzido de 48px */
    min-width: 85px; /* Reduzido de 90px */
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.nav-btn:hover {
    background: #f8f9fa;
    color: #5988A8 !important;
    border-color: rgba(89, 136, 168, 0.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.nav-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.nav-btn span {
    flex: 1 1 0%;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.card.no-shadow {
    border-radius: 0px !important;
}

.fs-6 {
    font-size: 1rem;
}

.vsm-table tr,
.vsm-table td,
.vsm-table th {
    padding: 0px;
    height: 10px !important;
}

.fw-5 {
    font-weight: 500;
}

.btn-default {
    border: 1px solid #CCC !important;
}

.fs-table {
    font-size: 11pt;
}

.search-input {
    font-size: 13pt;
    border-bottom: 1px solid #999;
    margin-bottom: 1rem;
}

.search-input:active,
.search-input:focus {
    border: none !important;
    border-bottom: 1px solid #92b9d3;
}

.p-horizontal-divider {
    width: calc(100% - 30px);
    margin: 15px;
    height: 1px;
    background: linear-gradient(to right, #EDEDED, #D2D2D2, #EDEDED);
}

.p-horizontal-divider.w-100 {
    width: 100%;
    margin-right: 0px;
    margin-left: 0px;
}

.p-horizontal-divider.primary {
    background: linear-gradient(to right, #dbf1ff, #accee7, #dbf1ff);
}

.p-horizontal-divider.light {
    background: linear-gradient(to right, #F9F9F9, #EEE 20%, #EEE 80%, #F9F9F9);
}

.p-horizontal-divider.m1 {
    margin: 5px;
}

.p-horizontal-divider.m2 {
    margin: 10px;
}

.p-horizontal-divider.m3 {
    margin: 15px;
}

.p-vertical-divider {
    width: 1px;
    margin: 0px 15px;
    height: 100%;
    background: linear-gradient(to bottom, #F0F0F0, #DEDEDE, #F0F0F0);
}

.p-vertical-divider.m1 {
    margin: 0px 5px;
}

.p-vertical-divider.m2 {
    margin: 0px 10px;
}

.p-vertical-divider.m3 {
    margin: 0px 15px;
}

.card {
    border: 1px solid #EEE;
}

.card.active {
    border-color: rgb(97, 167, 247);
    border-width: 2px;
}

.card.border-success {
    border-width: 2px;
    border-color: rgb(104, 201, 107) !important;
}

span.active {
    color: rgb(97, 167, 247);
}

svg.pointer {
    cursor: pointer;
}

svg.pointer.active {
    color: #546a8f;
}

svg.pointer:hover {
    color: #546a8f;
}

.btn-primary-light {
    background: #4487d3;
    color: #FFF;
}

.btn-primary-light:hover {
    background: #4e90db;
    color: #F8F8F8;
}

.text-primary-light {
    color: #4487d3;
}

.text-danger-dark {
    color: #ce3b3b;
}

.pointer.text-danger-dark:hover {
    color: #e45252;
}

.btn-vsm {
    padding: 2px 8px;
    margin: 0px;
    border-radius: 5px;
}

.btn-desmarcar-meta:hover {
    -webkit-box-shadow: inset 0px 0px 0px 10x #4CAF50;
    -moz-box-shadow: inset 0px 0px 0px 1px #4CAF50;
    box-shadow: inset 0px 0px 0px 1px #4CAF50;
    color: #4CAF50;
    background: rgba(255, 255, 255, 0);
}

.btn-outline-success:hover {
    background: #64c067 !important;
    color: #FFF !important;
}

.progress {
  border-radius: 0.375rem;
  border: 1px solid #CCC;
  height: var(--lumi-input-height);
  background-color: #f8f9fa;
  overflow: hidden;
  position: relative;
}

.progress .progress-bar {
  height: 100%;
  border-radius: 0.375rem;
  background-image: linear-gradient(to right, #4caf50, #45a049);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress .progress-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    font-weight: 500 !important;
    color: #444;
    z-index: 2;
    border-radius: 0.375rem;
}

.progress-sm {
    height: 22px !important;
}

.progress-sm .progress-value {
    font-size: 0.7rem !important;
    font-weight: 700 !important;
    padding: 0px 5px;
}

.progress-lg {
    height: calc(var(--lumi-input-height) + 4px) !important;
}

.progress-lg .progress-value {
    font-size: 0.75rem;
    padding: 0px 8px;
}

.custom-card {
    width: 100%;
    border: 1px solid #DDD;
    border-radius: 5px;
}

.custom-card-header {
    text-transform: uppercase;
    font-size: 12pt;
    font-weight: 500;
    padding: 7px 0px !important;
    background: linear-gradient(to right, #F8F8F8, #EEE, #F8F8F8);
    border-bottom: 1px solid #E2E2E2;
    text-align: center;
}

.primary .custom-card-header {
    background: linear-gradient(to right, #89abc4, #80a1bb, rgb(136, 171, 196));
    color: #FFF;
}

.box {
    /* box-sizing: border-box;
  width: calc(50% - 10px); */
    text-align: center;
    align-items: center;
    vertical-align: middle;

    border: 1px solid #DDD;
    border-radius: 5px;

    /* @media (max-width: 768px) {
    width: 100%;
  } */
}

.box p {
    text-align: center;
    padding: 0px 30px;
    padding-bottom: 20px;
}

.box .card {
    text-align: justify;
}

.box.primary,
.custom-card.primary {
    border: 1px solid #80a1bb !important;
}

.custom-card-body {
    text-align: justify;
    padding: 0px 15px;
    padding-bottom: 10px;
}

.border-between>[class*='col-']:before {
    background: #000 !important;
    bottom: 0;
    content: " ";
    left: 20;
    position: absolute;
    width: 20px;
    top: 20;
}

.border-between>[class*='col-']:first-child:before {
    display: none;
}

.section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 13pt;
}

.section-header svg {
    margin-bottom: 5px;
    font-size: 18pt;
}

input:read-only {
    background: #F8F8F8 !important;
}

div[class*="col-"].border-end {
    border-image: linear-gradient(180deg, #F0F0F0, #DEDEDE, #F0F0F0) 30;
    border-right-width: 1px;
    border-right-style: solid;
}

.border-end-md {
    border-right: none;
}

@media (min-width: 768px) {
    .border-end-md {
        border-image: linear-gradient(180deg, #F0F0F0, #DEDEDE, #F0F0F0) 30;
        border-right-width: 1px;
        border-right-style: solid;
    }
}

.table thead th {
    text-transform: none;
}

tr.positivo td, .text-positivo {
    color: rgb(46, 179, 46) !important;
}

tr.negativo td, .text-negativo {
    color: rgb(219, 82, 82) !important;
}

tr.atencao td, .text-atencao {
    color: rgb(238, 155, 0) !important;
}

tr.neutro td, .text-neutro {
    color: #666 !important;
}

.analises-table {
    border-radius: 5px;
}

.font-weight-300 {
    font-weight: 300;
}

.font-weight-400 {
    font-weight: 400;
}

.font-weight-500 {
    font-weight: 500;
}

.font-weight-600 {
    font-weight: 600;
}

.font-weight-700 {
    font-weight: 700;
}

.font-weight-800 {
    font-weight: 800;
}

a.hoverable:hover,
a.hoverable:hover svg {
    color: rgb(28, 137, 226) !important;
}

.pointer {
    cursor: pointer;
}

span.dropdown-toggle {
    margin-right: 3px;
}

.dropdown-menu-icons {
    display: none;
    border: 1px solid #CCC;
    min-width: 0px;
    width: 50px;
    padding: 0px;
    margin-bottom: 20px !important;
}

.dropdown-menu-icons.show {
    display: block !important;
}

/* Allow normal Bootstrap dropdowns to work */
.dropdown-menu:not(.dropdown-menu-icons) {
    display: none;
}

.dropdown-menu:not(.dropdown-menu-icons).show {
    display: block;
}

.dropdown-menu-icons li {
    width: 100%;
    text-align: center;
    padding: 4px;
    border-bottom: 1px solid #CCC;
}

.dropdown-menu-icons .dropdown-item {
    padding: 0px;
}

.dropdown-menu-icons li:first-child {
    padding: 6px 4px;
}

.dropdown-menu-icons li:last-child {
    padding: 6px 4px;
    border-bottom: none;
}

.dropdown-menu-icons.show::before {
    content: '';
}

.dropdown-menu-icons .dropdown-item:hover {
    background: inherit;
}

.dropdown-menu-icons li:hover {
    background: #EEE;
}

.input-sm {
    height: 32px;
}

.select-sm {
    padding-top: 3px;
    height: 32px;
}

p:not(.text-uppercase, .font-weight-bold),
.card-body {
    color: #545d6e;
}
p.text-uppercase, p.font-weight-bold {
    color: #7B809A;
}

.highlight {
    color: rgb(28, 137, 226);
}



.card.option-card {
    transition: transform 1.5s ease;
    text-align: center;
    border: 1px solid #DDD;
    border: 2px solid transparent;

    &:hover,
    &:focus {
        /* margin-top: 3px; */
        cursor: pointer;
        border: 2px solid #73A0DF;
    }
}

.option-image-container {
    width: 100%;
    text-align: center;
}

.option-image-container img {
    margin: 0 auto;
    width: 100%;
    height: auto;
    aspect-ratio: 9/6;
    object-fit: cover;
}


.tratamento-content {
    padding: 20px;
    padding-top: 5px;
}

.fs-14 {
    font-size: 14pt;
}

.fs-15 {
    font-size: 15pt;
}

.btn-edit {
    width: 140px;
}

.card-top-border {
    border-top: 1px solid #DDD;
}
.input-group input,
.input-group input:hover,
.input-group input:active,
.input-group input:focus,
.l-input-group input,
.l-input-group input:hover,
.l-input-group input:active,
.l-input-group input:focus {
    background: #FFF;
}

.l-input-group {
    display: flex;
    flex-direction: row;;
    justify-content: center;
    border-radius: 5px;
}

.l-input-group > * {
    height: 32px;
    border-top: 1px solid #D0D0D0;
    border-bottom: 1px solid #D0D0D0;
    border-radius: 0px;
}

.l-input-group > :first-child {
    border-left: 1px solid #D0D0D0;
    border-radius: 5px 0px 0px 5px;
}

.l-input-group > :last-child {
    border-right: 1px solid #D0D0D0;
    border-radius: 0px 5px 5px 0px;
}

.l-input-group input {
    background: #FFF;
}

.l-input-group span {
    font-weight: 500;
    padding: 3px 12px;
    vertical-align: middle;
    background: #F2F2F2;
}

.inline-input {
    display: inline;
    background: #FFF;
}


.fadeHeight-enter-active,
.fadeHeight-leave-active {
  transition: all 1s;
}
.fadeHeight-enter-from,
.fadeHeight-leave-to
{
  opacity: 0;
  max-height: 0px;
}
.fadeHeight-enter-to,
.fadeHeight-leave-from
{
  opacity: 1;
  max-height: 230px;
}

/* Lumi Modal Fade Effects */
/* Base modal fade effect */
.modal.lumi-fade {
  transition: opacity 0.15s linear;
}

.modal.lumi-fade .modal-dialog {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.3s ease;
  transform: translate(0, -30px) scale(0.95);
  opacity: 0;
  will-change: transform, opacity;
}

.modal.lumi-fade.show .modal-dialog {
  transform: translate(0, 0) scale(1);
  opacity: 1;
}

/* Modal content animation */
.modal.lumi-fade .modal-content {
  transition: transform 0.2s ease-out, opacity 0.2s ease;
  transform-origin: center top;
  transform: translateY(10px);
  opacity: 0;
}

.modal.lumi-fade.show .modal-content {
  transform: translateY(0);
  opacity: 1;
  transition-delay: 0.1s;
}

/* Closing animation */
.modal.lumi-fade.show.modal-closing .modal-dialog {
  transform: translate(0, 20px) scale(0.95);
  opacity: 0;
  transition: transform 0.25s cubic-bezier(0.34, 0, 0.64, 1), opacity 0.25s ease;
}

.modal.lumi-fade.show.modal-closing .modal-content {
  transform: translateY(-10px);
  opacity: 0;
  transition: transform 0.2s ease-in, opacity 0.2s ease;
  transition-delay: 0s;
}

/* Enhanced backdrop */
.modal.lumi-fade + .modal-backdrop.fade {
  transition: opacity 0.25s linear;
}

.modal.lumi-fade + .modal-backdrop.show {
  opacity: 0.6;
}

[v-cloak].init-hidden {
    display: none !important;
}

[v-cloak] { display:none; }

.label-highlight {
    width: 100%;
    padding: 6px 10px !important;
    background: linear-gradient(to bottom, #F8F8F8, #EEE);
    border: 1px solid #DDD;
    border-radius: 5px;
    font-size: 12pt;
    margin: 9px 0px;
}

.label-big {
    font-size: 12pt !important;
}

.pt5 {
    padding-top: 5px !important;
}

/* Swal 2 Customizations */

/* Garantir que o SweetAlert2 sempre apareça acima de qualquer modal */
.swal2-container {
    z-index: 99999 !important;
}

.swal2-popup {
    z-index: 100000 !important;
}

button.swal2-confirm {
    color: #FFF;
	background: linear-gradient(195deg, #7aa3c0 0%, #32668c 100%);
}

button.swal2-confirm:hover {
    color: #FFF;
    background: linear-gradient(195deg, #6495b3 0%, #2c5c7c 100%) !important;
}

button.swal2-cancel {
    color: #FFF;
	background: linear-gradient(195deg, #F8BB86 0%, #dd9f68 100%);
}

button.swal2-cancel:hover {
    color: #FFF;
    background: linear-gradient(195deg, rgb(189, 149, 90) 0%, rgb(189, 149, 90) 100%) !important;
}

input.input-sm {
    height: 32px;
}

.nav-btn.highlight {
    background: #5988A8;
    border: 1px solid #5988A8;
    box-shadow: 0 4px 12px rgba(89, 136, 168, 0.25);
    color: #ffffff !important;
}

.nav-btn.highlight:hover {
    background: #4a7a96;
    border-color: #4a7a96;
    box-shadow: 0 6px 16px rgba(89, 136, 168, 0.35);
    transform: translateY(-1px);
}

.nav-btn.highlight span, .nav-btn.highlight div, .nav-btn.highlight i {
    color: #ffffff !important;
    font-weight: 600;
}

/* Melhorias nos ícones dos botões da sidebar */
.nav-btn i, .nav-btn .fa-icon, .nav-btn .v-icon {
    font-size: 1.1rem !important;
    transition: all 0.2s ease;
    color: #344767 !important;
}

.nav-btn .material-icons-round {
    font-variation-settings: 'FILL' 0, 'wght' 500, 'GRAD' 0, 'opsz' 24;
}

.nav-btn:hover i, .nav-btn:hover .fa-icon, .nav-btn:hover .v-icon {
    color: #5988A8 !important;
}

.nav-btn.highlight i, .nav-btn.highlight .fa-icon, .nav-btn.highlight .v-icon {
    font-variation-settings: 'FILL' 1, 'wght' 600, 'GRAD' 0, 'opsz' 24;
    color: #ffffff !important;
}

/* Espaçamento entre ícone e texto */
.nav-btn div.me-2 {
    margin-right: 0.5rem !important; /* Reduzido de 0.6rem */
    min-width: 22px; /* Reduzido de 24px */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Garantir que todos os tipos de ícones herdem a cor correta */
.nav-btn div.me-2 * {
    color: inherit !important;
    transition: color 0.2s ease;
}

.nav-btn:hover div.me-2 * {
    color: #5988A8 !important;
}

.nav-btn.highlight div.me-2 * {
    color: #ffffff !important;
}

/* Títulos dos grupos da sidebar */
.sidenav .navbar-nav .nav-item h6.text-xs {
    color: #5988A8 !important;
    font-weight: 600 !important;
    font-size: 0.7rem !important;
    letter-spacing: 1px;
    margin-bottom: 0.8rem !important;
    margin-top: 1.2rem !important;
    text-transform: uppercase;
}

/* ===== AJUSTES SUTIS LUMI SIDENAV ===== */

/* Ícone do header com tamanho otimizado */
.lumi-sidenav-header-icon {
    font-size: 50pt !important; /* Reduzido de 55pt para 50pt */
    color: #5988A8 !important;
    margin: 0.4rem !important; /* Reduzido de 0.5rem */
    margin-top: 0.7rem !important; /* Reduzido de 0.8rem */
    transition: all 0.3s ease;
}

/* Logo do footer com tamanho otimizado */
.lumi-sidenav-footer-logo {
    height: 2rem !important; /* Reduzido de 2.7rem para 2.4rem */
    max-height: 2rem !important;
    opacity: 0.9 !important; /* Opacidade reduzida para 90% */
    transition: all 0.3s ease;
}

/* Footer da sidenav com padding-bottom */
.sidenav-footer {
    padding-bottom: 1.3rem !important; /* Espaçamento do fundo da tela */
}

/* Ajustes para telas com altura limitada */
@media (max-height: 690px) {
    .lumi-sidenav-footer-logo {
        height: 2rem !important;
        max-height: 2rem !important;
        margin-top: 0.3rem !important;
        opacity: 0.85 !important; /* Opacidade ainda menor em telas baixas */
    }

    .sidenav-footer {
        padding-bottom: 0.75rem !important; /* Menos padding em telas baixas */
    }
}

@media (max-height: 600px) {
    .lumi-sidenav-header-icon {
        font-size: 42pt !important;
        margin: 0.3rem !important;
        margin-top: 0.5rem !important;
    }

    .lumi-sidenav-footer-logo {
        height: 1.8rem !important;
        max-height: 1.8rem !important;
        opacity: 0.8 !important; /* Opacidade ainda menor em telas muito baixas */
    }

    .sidenav-footer {
        padding-bottom: 0.5rem !important; /* Padding mínimo em telas muito baixas */
    }
}

.contains-dropdown .v-table__wrapper {
    overflow: visible;
}


/*
mViewer
*/

.viewer-title {
    color: #ffffff !important;
    font-size: 18pt !important;
}

.next-btn-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    background: linear-gradient(to right, rgba(202, 219, 211, 0.3), rgba(221, 221, 221, 0.3) 15%, rgba(238, 238, 238, 0.3));
    border-radius: 5px;
    border: 1px solid #DDD;
}

.next-btn-container button {
    background: linear-gradient(to bottom, #48a74b, #4CAF50, #48a74b);
    box-shadow: 0 3px 3px 0 rgba(27, 100, 68, 0.15), 0 3px 1px -2px rgba(27, 100, 68, 0.2), 0 1px 5px 0 rgba(27, 100, 68, 0.15);
}

.next-btn-container button:hover {
    filter: brightness(1.03);
    box-shadow: 0 6px 10px 0px rgba(27, 100, 68, 0.2), 0 4px 12px 0 rgba(27, 100, 68, 0.07), 0 4px 8px -2px rgba(27, 100, 68, 0.1);
}

.viewer-footer {
    background: rgba(0, 0, 0, 0.5) !important;
}

.viewer-title {
}

.viewer-toolbar {
}

.viewer-toolbar > ul > li {
    display: flex !important;
    justify-content: center;
    align-items: center;
    border-radius: 3px !important;
    width: 30px !important;
    height: 30px !important;
    border: 2px solid #333 !important;
    background: #1f4d70 !important;
}

.viewer-toolbar > ul > .viewer-large {
    width: 35px !important;
    height: 35px !important;
}

.novo-contato-row {
    /* border-radius: 0px !important; */
}

.novo-contato-row > button {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
}

.novo-contato-row > button.dropup {
    background: #F3F3F3;
    padding-top: 1px;
    border: 1px solid #DDD;
    margin: 0px !important;
    border-radius: 0.375rem;
    padding-left: 0.6rem;
    padding-right: 0.13em;
    width: 60px;
}

.novo-contato-row > button.dropup:hover {
    background: #E9E9E9;
}

.novo-contato-row > button:last-child {
    border-radius: 0 0.375rem 0.375rem 0;
}

.novo-contato-row > input {
    font-size: 0.875rem !important;
}


@media (max-width: 576px) {
    .novo-contato-row > button.dropup {
        width: 50px;
    }
}



@media (max-width: 768px) {
    .moving-tab {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    .back-btn.edit-mode {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
    }
}

/* Ocultar o botão Voltar quando o modo de edição está ativo em todas as resoluções */
.back-btn.edit-mode {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

.btn.btn-sm i, .btn-group-sm > .btn i {
    font-size: 0.9rem !important;
}

.btn {
    margin-bottom: 0 !important;
}

.btn-outline-primary {
  border-color: #007bff !important;
  color: #007bff !important;
  background: transparent !important;
}

.btn-outline-primary:hover {
  background: #007bff !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

.btn-outline-info {
  border-color: #17a2b8 !important;
  color: #17a2b8 !important;
  background: transparent !important;
}

.btn-outline-info:hover {
  background: #17a2b8 !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3) !important;
}

/* Fix para dropdown de ortodontistas na tela de pacientes */
.vue3-easy-data-table__main.fixed-header th {
    z-index: 0 !important;
}

.vue3-easy-data-table__main {
    z-index: 0 !important;
}

.vue3-easy-data-table {
    z-index: 0 !important;
}

/* Garantir que dropdowns sempre fiquem na frente */
.ortodontistas-dropdown {
    z-index: 10 !important;
}

